@echo off
echo 🚀 Starting Phi-3 GGUF Chat...
echo.

REM Activate virtual environment
call phi3_env\Scripts\activate.bat

if errorlevel 1 (
    echo ❌ Failed to activate virtual environment
    echo Trying without virtual environment...
    echo.
    goto :run_script
)

echo ✅ Virtual environment activated

:run_script
echo 🤖 Starting GGUF chat interface...
echo.

REM Run the GGUF chat script
python phi3_gguf_chat.py

echo.
echo 👋 Chat session ended
pause
