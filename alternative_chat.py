#!/usr/bin/env python3
"""
Alternative chat script that bypasses local sharded loading issues
"""

import os
import torch
from transformers import AutoModelForCausalLM, AutoTokenizer, pipeline
import warnings
warnings.filterwarnings("ignore")

def try_huggingface_direct():
    """Try loading directly from Hugging Face instead of local files."""
    print("🔄 Method 1: Loading from Hugging Face directly...")
    
    try:
        model_name = "microsoft/Phi-3-mini-4k-instruct"
        
        print("📝 Loading tokenizer from HF...")
        tokenizer = AutoTokenizer.from_pretrained(model_name, trust_remote_code=True)
        
        print("🧠 Loading model from HF (this may take time)...")
        model = AutoModelForCausalLM.from_pretrained(
            model_name,
            torch_dtype=torch.float16,
            device_map="cpu",
            trust_remote_code=True,
            attn_implementation="eager"
        )
        
        print("✅ Hugging Face loading successful!")
        return model, tokenizer, "huggingface"
        
    except Exception as e:
        print(f"❌ Hugging Face loading failed: {e}")
        return None, None, None

def try_pipeline_approach():
    """Try using transformers pipeline which handles loading differently."""
    print("🔄 Method 2: Using pipeline approach...")
    
    try:
        model_path = r".\phi-3-mini"
        
        print("🔧 Creating text generation pipeline...")
        pipe = pipeline(
            "text-generation",
            model=model_path,
            tokenizer=model_path,
            torch_dtype=torch.float16,
            device_map="cpu",
            trust_remote_code=True,
            model_kwargs={"attn_implementation": "eager"}
        )
        
        print("✅ Pipeline loading successful!")
        return pipe, None, "pipeline"
        
    except Exception as e:
        print(f"❌ Pipeline loading failed: {e}")
        return None, None, None

def try_manual_shard_loading():
    """Try manually loading the shards one by one."""
    print("🔄 Method 3: Manual shard loading...")
    
    try:
        from safetensors import safe_open
        import json
        
        model_path = r".\phi-3-mini"
        
        # Load tokenizer first
        print("📝 Loading tokenizer...")
        tokenizer = AutoTokenizer.from_pretrained(model_path, trust_remote_code=True)
        
        # Read the index file
        with open(os.path.join(model_path, "model.safetensors.index.json"), 'r') as f:
            index = json.load(f)
        
        print("📋 Found model shards:")
        shard_files = set(index["weight_map"].values())
        for shard in shard_files:
            shard_path = os.path.join(model_path, shard)
            if os.path.exists(shard_path):
                size = os.path.getsize(shard_path) / (1024**3)  # GB
                print(f"   ✅ {shard} ({size:.1f} GB)")
            else:
                print(f"   ❌ {shard} (missing)")
                return None, None, None
        
        # Try loading with explicit shard handling
        print("🧠 Loading model with manual shard handling...")
        model = AutoModelForCausalLM.from_pretrained(
            model_path,
            torch_dtype=torch.float32,  # Use float32 for stability
            device_map="cpu",
            trust_remote_code=True,
            use_safetensors=True,
            attn_implementation="eager",
            low_cpu_mem_usage=True,
            max_memory={0: "6GB", "cpu": "8GB"}  # Limit memory usage
        )
        
        print("✅ Manual loading successful!")
        return model, tokenizer, "manual"
        
    except Exception as e:
        print(f"❌ Manual loading failed: {e}")
        return None, None, None

def try_gguf_alternative():
    """Suggest GGUF alternative if available."""
    print("🔄 Method 4: Checking for GGUF alternative...")
    
    # Check if user has downloaded GGUF version
    possible_gguf_paths = [
        r".\phi-3-mini-gguf",
        r".\phi-3-mini.gguf",
        r".\phi-3-mini-4k-instruct.gguf"
    ]
    
    for path in possible_gguf_paths:
        if os.path.exists(path):
            print(f"✅ Found GGUF file: {path}")
            print("💡 GGUF files require different loading (llama-cpp-python)")
            return None, None, "gguf_found"
    
    print("❌ No GGUF files found")
    print("💡 Consider downloading GGUF version for easier loading")
    return None, None, None

def generate_with_model(model, tokenizer, user_input):
    """Generate response with loaded model."""
    try:
        prompt = f"<|system|>\nYou are a helpful assistant.<|end|>\n<|user|>\n{user_input}<|end|>\n<|assistant|>\n"
        
        inputs = tokenizer(prompt, return_tensors="pt")
        
        with torch.no_grad():
            outputs = model.generate(
                inputs.input_ids,
                max_new_tokens=100,
                temperature=0.7,
                do_sample=True,
                pad_token_id=tokenizer.eos_token_id
            )
        
        response = tokenizer.decode(outputs[0][inputs.input_ids.shape[1]:], skip_special_tokens=True)
        return response.strip()
        
    except Exception as e:
        return f"Generation error: {e}"

def generate_with_pipeline(pipe, user_input):
    """Generate response with pipeline."""
    try:
        messages = [
            {"role": "system", "content": "You are a helpful assistant."},
            {"role": "user", "content": user_input}
        ]
        
        response = pipe(messages, max_new_tokens=100, temperature=0.7)
        return response[0]['generated_text'][-1]['content']
        
    except Exception as e:
        return f"Pipeline error: {e}"

def main():
    """Try different loading methods until one works."""
    print("=" * 60)
    print("🔧 Alternative Phi-3 Loading Methods")
    print("=" * 60)
    
    methods = [
        try_huggingface_direct,
        try_pipeline_approach,
        try_manual_shard_loading,
        try_gguf_alternative
    ]
    
    model, tokenizer, method_type = None, None, None
    pipe = None
    
    for method in methods:
        result = method()
        if result[0] is not None:
            if method_type == "pipeline":
                pipe = result[0]
                method_type = result[2]
            else:
                model, tokenizer, method_type = result
            break
    
    if model is None and pipe is None:
        print("\n❌ All loading methods failed.")
        print("\n💡 Suggestions:")
        print("   1. Download GGUF version: https://huggingface.co/microsoft/Phi-3-mini-4k-instruct-gguf")
        print("   2. Try online version: https://huggingface.co/spaces/microsoft/Phi-3-mini-4k-instruct")
        print("   3. Check if you have enough RAM (8GB+ needed)")
        print("   4. Try restarting your computer to free memory")
        input("Press Enter to exit...")
        return
    
    print(f"\n✅ Successfully loaded using {method_type} method!")
    print("\n💬 Starting chat (type 'quit' to exit)...")
    print("=" * 40)
    
    # Chat loop
    while True:
        try:
            user_input = input("\nYou: ").strip()
            
            if user_input.lower() in ['quit', 'exit']:
                break
            
            if not user_input:
                continue
            
            print("🤔 Thinking...")
            
            if pipe:
                response = generate_with_pipeline(pipe, user_input)
            else:
                response = generate_with_model(model, tokenizer, user_input)
            
            print(f"Bot: {response}")
            
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"Error: {e}")

if __name__ == "__main__":
    main()
