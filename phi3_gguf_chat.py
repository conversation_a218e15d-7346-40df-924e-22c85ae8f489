#!/usr/bin/env python3
"""
Chat script for Phi-3 GGUF model
Much more reliable than the sharded safetensors version!
"""

import os
import sys
import time

def check_llama_cpp():
    """Check if llama-cpp-python is installed."""
    try:
        from llama_cpp import <PERSON><PERSON>a
        return True
    except ImportError:
        return False

def install_llama_cpp():
    """Install llama-cpp-python."""
    print("📦 Installing llama-cpp-python...")
    try:
        import subprocess
        subprocess.check_call([sys.executable, "-m", "pip", "install", "llama-cpp-python"])
        print("✅ Installation complete!")
        return True
    except Exception as e:
        print(f"❌ Installation failed: {e}")
        return False

def find_gguf_model():
    """Find the GGUF model file."""
    possible_paths = [
        r".\phi-3-gguf\Phi-3-mini-4k-instruct-q4.gguf",
        r".\phi-3-gguf\phi-3-mini-4k-instruct-q4.gguf",
        r".\Phi-3-mini-4k-instruct-q4.gguf",
        r".\phi-3-mini-4k-instruct-q4.gguf",
        r".\phi-3-gguf\Phi-3-mini-4k-instruct.gguf",
        r".\phi-3-mini-4k-instruct.gguf"
    ]
    
    print("🔍 Looking for GGUF model file...")
    
    for path in possible_paths:
        if os.path.exists(path):
            size = os.path.getsize(path) / (1024**3)  # GB
            print(f"✅ Found: {path} ({size:.1f} GB)")
            return path
    
    # If not found, list what's in the directory
    print("❌ GGUF model not found in expected locations.")
    print("\n📁 Checking current directory:")
    
    for item in os.listdir("."):
        if item.endswith(".gguf"):
            size = os.path.getsize(item) / (1024**3)
            print(f"   Found GGUF: {item} ({size:.1f} GB)")
            return item
        elif os.path.isdir(item) and "gguf" in item.lower():
            print(f"   Found directory: {item}")
            for subitem in os.listdir(item):
                if subitem.endswith(".gguf"):
                    full_path = os.path.join(item, subitem)
                    size = os.path.getsize(full_path) / (1024**3)
                    print(f"      GGUF file: {subitem} ({size:.1f} GB)")
                    return full_path
    
    return None

def load_model(model_path):
    """Load the GGUF model with speed optimizations."""
    print(f"🔄 Loading model: {os.path.basename(model_path)}")
    print("   Optimizing for speed...")

    try:
        from llama_cpp import Llama
        import multiprocessing

        # Get CPU core count for optimal threading
        cpu_cores = multiprocessing.cpu_count()
        optimal_threads = min(cpu_cores, 8)  # Cap at 8 threads

        print(f"   Using {optimal_threads} threads (detected {cpu_cores} CPU cores)")

        # Load with speed-optimized settings
        llm = Llama(
            model_path=model_path,
            n_ctx=2048,  # Reduced context for speed (was 4096)
            n_threads=optimal_threads,  # Use optimal thread count
            n_gpu_layers=0,  # CPU only
            verbose=False,
            use_mmap=True,  # Memory mapping
            use_mlock=False,
            n_batch=256,  # Smaller batch for faster processing (was 512)
            f16_kv=True,  # Use 16-bit for key-value cache (faster)
            logits_all=False,  # Don't compute logits for all tokens
            vocab_only=False,
            use_mlock=False,
            embedding=False,
            last_n_tokens_size=64,  # Smaller context for repetition penalty
        )

        print("✅ Model loaded with speed optimizations!")
        return llm

    except Exception as e:
        print(f"❌ Failed to load model: {e}")
        return None

def generate_response(llm, user_input, max_tokens=100, temperature=0.7):
    """Generate a response using the GGUF model with speed optimizations."""
    try:
        # Shorter, more efficient prompt format
        prompt = f"User: {user_input}\nAssistant:"

        # Generate response with speed-focused settings
        response = llm(
            prompt,
            max_tokens=max_tokens,  # Default reduced to 100 for speed
            temperature=temperature,
            top_p=0.95,  # Slightly higher for better quality
            top_k=40,  # Add top-k sampling for speed
            repeat_penalty=1.05,  # Reduced penalty for speed
            stop=["\nUser:", "\n\n", "User:", "Assistant:"],  # Simpler stop tokens
            echo=False,
            stream=False,  # Disable streaming for simplicity
            tfs_z=1.0,  # Tail free sampling disabled
            typical_p=1.0,  # Typical sampling disabled
            mirostat_mode=0,  # Disable mirostat for speed
        )

        # Extract and clean the generated text
        generated_text = response['choices'][0]['text'].strip()

        # Remove any leftover prompt parts
        if generated_text.startswith("Assistant:"):
            generated_text = generated_text[10:].strip()

        return generated_text

    except Exception as e:
        return f"❌ Generation error: {e}"

def print_welcome():
    """Print welcome message."""
    print("\n" + "=" * 60)
    print("🤖 Phi-3 GGUF Chat Interface")
    print("=" * 60)
    print("Welcome! You're now using the GGUF version of Phi-3.")
    print("This should be much more reliable than the safetensors version!")
    print()
    print("Commands:")
    print("  • Type your message and press Enter")
    print("  • 'settings' - Adjust generation parameters")
    print("  • 'clear' - Clear the screen")
    print("  • 'quit' or 'exit' - End the session")
    print("=" * 60)

def adjust_settings():
    """Allow user to adjust generation settings."""
    print("\n⚙️  Generation Settings:")
    print("1. Max tokens (current: will be shown)")
    print("2. Temperature (current: will be shown)")
    print("3. Back to chat")
    
    # This is a simplified version - you can expand it
    choice = input("\nEnter choice (1-3): ").strip()
    
    if choice == "1":
        try:
            new_tokens = int(input("Enter max tokens (50-500): "))
            if 50 <= new_tokens <= 500:
                print(f"✅ Max tokens will be set to {new_tokens} for next response")
                return {"max_tokens": new_tokens}
            else:
                print("❌ Please enter a value between 50 and 500")
        except ValueError:
            print("❌ Please enter a valid number")
    
    elif choice == "2":
        try:
            new_temp = float(input("Enter temperature (0.1-1.0): "))
            if 0.1 <= new_temp <= 1.0:
                print(f"✅ Temperature will be set to {new_temp} for next response")
                return {"temperature": new_temp}
            else:
                print("❌ Please enter a value between 0.1 and 1.0")
        except ValueError:
            print("❌ Please enter a valid number")
    
    return {}

def main():
    """Main chat function."""
    print("🚀 Starting Phi-3 GGUF Chat...")
    
    # Check if llama-cpp-python is installed
    if not check_llama_cpp():
        print("❌ llama-cpp-python not found.")
        choice = input("Would you like to install it? (y/n): ").lower()
        if choice == 'y':
            if not install_llama_cpp():
                print("❌ Installation failed. Please install manually:")
                print("   pip install llama-cpp-python")
                input("Press Enter to exit...")
                return
            print("✅ Please restart the script after installation.")
            input("Press Enter to exit...")
            return
        else:
            print("❌ Cannot proceed without llama-cpp-python.")
            input("Press Enter to exit...")
            return
    
    # Find the GGUF model
    model_path = find_gguf_model()
    if not model_path:
        print("❌ No GGUF model found.")
        print("💡 Make sure you've downloaded the GGUF version of Phi-3.")
        input("Press Enter to exit...")
        return
    
    # Load the model
    llm = load_model(model_path)
    if not llm:
        print("❌ Failed to load model.")
        input("Press Enter to exit...")
        return
    
    # Start chat
    print_welcome()
    
    # Default settings
    settings = {"max_tokens": 200, "temperature": 0.7}
    
    while True:
        try:
            user_input = input("\n💬 You: ").strip()
            
            if not user_input:
                continue
            
            # Handle commands
            if user_input.lower() in ['quit', 'exit', 'bye']:
                print("👋 Goodbye! Thanks for chatting!")
                break
            
            elif user_input.lower() == 'clear':
                os.system('cls' if os.name == 'nt' else 'clear')
                print_welcome()
                continue
            
            elif user_input.lower() == 'settings':
                new_settings = adjust_settings()
                settings.update(new_settings)
                continue
            
            # Generate response
            print("🤔 Thinking...")
            start_time = time.time()
            
            response = generate_response(
                llm, 
                user_input, 
                max_tokens=settings["max_tokens"],
                temperature=settings["temperature"]
            )
            
            end_time = time.time()
            
            print(f"\n🤖 Phi-3: {response}")
            print(f"⏱️  Response time: {end_time - start_time:.1f}s")
            
        except KeyboardInterrupt:
            print("\n\n👋 Chat interrupted. Goodbye!")
            break
        except Exception as e:
            print(f"\n❌ An error occurred: {e}")
            print("Type 'quit' to exit or continue chatting.")

if __name__ == "__main__":
    main()
