#!/usr/bin/env python3
"""
Debug script to identify and fix model loading issues
"""

import os
import sys
import traceback

def check_environment():
    """Check Python environment and packages."""
    print("🔍 Checking environment...")
    print(f"Python version: {sys.version}")
    
    try:
        import torch
        print(f"✅ PyTorch: {torch.__version__}")
    except ImportError as e:
        print(f"❌ PyTorch not found: {e}")
        return False
    
    try:
        import transformers
        print(f"✅ Transformers: {transformers.__version__}")
    except ImportError as e:
        print(f"❌ Transformers not found: {e}")
        return False
    
    return True

def check_model_files():
    """Check if all model files exist."""
    print("\n🔍 Checking model files...")
    model_path = r".\phi-3-mini"
    
    if not os.path.exists(model_path):
        print(f"❌ Model directory not found: {model_path}")
        return False
    
    required_files = [
        "config.json",
        "tokenizer.json",
        "tokenizer_config.json",
        "model.safetensors.index.json",
        "model-00001-of-00002.safetensors",
        "model-00002-of-00002.safetensors"
    ]
    
    for file in required_files:
        file_path = os.path.join(model_path, file)
        if os.path.exists(file_path):
            size = os.path.getsize(file_path)
            print(f"✅ {file} ({size:,} bytes)")
        else:
            print(f"❌ Missing: {file}")
            return False
    
    return True

def test_tokenizer_only():
    """Test loading just the tokenizer."""
    print("\n🔍 Testing tokenizer loading...")
    
    try:
        from transformers import AutoTokenizer
        model_path = r".\phi-3-mini"
        
        tokenizer = AutoTokenizer.from_pretrained(model_path, trust_remote_code=True)
        print("✅ Tokenizer loaded successfully")
        
        # Test tokenization
        test_text = "Hello world"
        tokens = tokenizer(test_text, return_tensors="pt")
        print(f"✅ Tokenization test: '{test_text}' -> {tokens.input_ids.shape[1]} tokens")
        
        return tokenizer
        
    except Exception as e:
        print(f"❌ Tokenizer loading failed:")
        print(f"   Error: {e}")
        traceback.print_exc()
        return None

def test_model_loading_verbose():
    """Test model loading with detailed error reporting."""
    print("\n🔍 Testing model loading (verbose)...")
    
    try:
        from transformers import AutoModelForCausalLM
        model_path = r".\phi-3-mini"
        
        print("📝 Loading with minimal settings...")
        model = AutoModelForCausalLM.from_pretrained(
            model_path,
            trust_remote_code=True,
            torch_dtype="auto",
            device_map="cpu",
            use_safetensors=True,
            low_cpu_mem_usage=True
        )
        
        print("✅ Model loaded successfully!")
        return model
        
    except Exception as e:
        print(f"❌ Model loading failed:")
        print(f"   Error type: {type(e).__name__}")
        print(f"   Error message: {e}")
        print("\n📋 Full traceback:")
        traceback.print_exc()
        return None

def test_simple_generation(model, tokenizer):
    """Test simple text generation."""
    print("\n🔍 Testing text generation...")
    
    try:
        prompt = "Hello, I am"
        inputs = tokenizer(prompt, return_tensors="pt")
        
        print(f"📝 Input: '{prompt}'")
        print("🤔 Generating...")
        
        with torch.no_grad():
            outputs = model.generate(
                inputs.input_ids,
                max_new_tokens=10,
                do_sample=False,
                pad_token_id=tokenizer.eos_token_id
            )
        
        response = tokenizer.decode(outputs[0], skip_special_tokens=True)
        print(f"✅ Generated: '{response}'")
        return True
        
    except Exception as e:
        print(f"❌ Generation failed:")
        print(f"   Error: {e}")
        traceback.print_exc()
        return False

def main():
    """Run all diagnostic tests."""
    print("=" * 60)
    print("🔧 Phi-3 Model Debug Script")
    print("=" * 60)
    
    # Check environment
    if not check_environment():
        print("\n❌ Environment check failed. Please install required packages.")
        input("Press Enter to exit...")
        return
    
    # Check model files
    if not check_model_files():
        print("\n❌ Model files check failed. Please check your model download.")
        input("Press Enter to exit...")
        return
    
    # Test tokenizer
    tokenizer = test_tokenizer_only()
    if tokenizer is None:
        print("\n❌ Tokenizer test failed. Cannot proceed.")
        input("Press Enter to exit...")
        return
    
    # Test model loading
    model = test_model_loading_verbose()
    if model is None:
        print("\n❌ Model loading failed.")
        print("\n💡 Common solutions:")
        print("   1. Try updating transformers: pip install --upgrade transformers")
        print("   2. Try updating torch: pip install --upgrade torch")
        print("   3. Check if you have enough RAM (model needs ~8GB)")
        print("   4. Try redownloading the model")
        input("Press Enter to exit...")
        return
    
    # Test generation
    if test_simple_generation(model, tokenizer):
        print("\n🎉 All tests passed! Your model is working correctly.")
        
        # Offer to start simple chat
        choice = input("\nWould you like to start a simple chat? (y/n): ").lower()
        if choice == 'y':
            simple_chat(model, tokenizer)
    else:
        print("\n❌ Generation test failed.")
    
    input("Press Enter to exit...")

def simple_chat(model, tokenizer):
    """Very simple chat interface."""
    print("\n" + "=" * 40)
    print("💬 Simple Chat (type 'quit' to exit)")
    print("=" * 40)
    
    while True:
        try:
            user_input = input("\nYou: ").strip()
            
            if user_input.lower() in ['quit', 'exit']:
                break
            
            if not user_input:
                continue
            
            # Simple generation
            inputs = tokenizer(f"User: {user_input}\nBot:", return_tensors="pt")
            
            with torch.no_grad():
                outputs = model.generate(
                    inputs.input_ids,
                    max_new_tokens=50,
                    temperature=0.7,
                    do_sample=True,
                    pad_token_id=tokenizer.eos_token_id
                )
            
            response = tokenizer.decode(outputs[0][inputs.input_ids.shape[1]:], skip_special_tokens=True)
            print(f"Bot: {response.strip()}")
            
        except KeyboardInterrupt:
            break
        except Exception as e:
            print(f"Error: {e}")

if __name__ == "__main__":
    # Import torch here to catch import errors
    try:
        import torch
    except ImportError:
        print("❌ PyTorch not found. Please activate your virtual environment.")
        input("Press Enter to exit...")
        sys.exit(1)
    
    main()
