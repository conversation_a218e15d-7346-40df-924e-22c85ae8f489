#!/usr/bin/env python3
"""
Most basic chat script for Phi-3 Mini - tries multiple loading methods
"""

import os
import torch
from transformers import AutoModelForCausalLM, AutoTokenizer
import warnings

warnings.filterwarnings("ignore")

def try_load_method_1():
    """Method 1: Basic CPU loading"""
    print("🔄 Trying Method 1: Basic CPU loading...")
    model_path = r".\phi-3-mini"
    
    try:
        tokenizer = AutoTokenizer.from_pretrained(model_path)
        if tokenizer.pad_token is None:
            tokenizer.pad_token = tokenizer.eos_token
            
        model = AutoModelForCausalLM.from_pretrained(
            model_path,
            torch_dtype=torch.float32,
            device_map="cpu",
            trust_remote_code=True
        )
        return model, tokenizer, "Method 1"
    except Exception as e:
        print(f"❌ Method 1 failed: {e}")
        return None, None, None

def try_load_method_2():
    """Method 2: Load without device_map"""
    print("🔄 Trying Method 2: Load without device_map...")
    model_path = r".\phi-3-mini"
    
    try:
        tokenizer = AutoTokenizer.from_pretrained(model_path)
        if tokenizer.pad_token is None:
            tokenizer.pad_token = tokenizer.eos_token
            
        model = AutoModelForCausalLM.from_pretrained(
            model_path,
            trust_remote_code=True,
            low_cpu_mem_usage=True
        )
        return model, tokenizer, "Method 2"
    except Exception as e:
        print(f"❌ Method 2 failed: {e}")
        return None, None, None

def try_load_method_3():
    """Method 3: Minimal loading"""
    print("🔄 Trying Method 3: Minimal loading...")
    model_path = r".\phi-3-mini"
    
    try:
        tokenizer = AutoTokenizer.from_pretrained(model_path)
        model = AutoModelForCausalLM.from_pretrained(model_path)
        return model, tokenizer, "Method 3"
    except Exception as e:
        print(f"❌ Method 3 failed: {e}")
        return None, None, None

def load_model():
    """Try different loading methods until one works."""
    methods = [try_load_method_1, try_load_method_2, try_load_method_3]
    
    for method in methods:
        model, tokenizer, method_name = method()
        if model is not None:
            print(f"✅ Success with {method_name}!")
            return model, tokenizer
    
    print("❌ All loading methods failed!")
    return None, None

def simple_generate(model, tokenizer, text, max_new_tokens=100):
    """Very simple text generation."""
    try:
        # Simple prompt format
        prompt = f"User: {text}\nAssistant:"
        
        inputs = tokenizer(prompt, return_tensors="pt")
        
        with torch.no_grad():
            outputs = model.generate(
                inputs.input_ids,
                max_new_tokens=max_new_tokens,
                temperature=0.7,
                do_sample=True,
                pad_token_id=tokenizer.eos_token_id
            )
        
        # Get only the new tokens
        new_tokens = outputs[0][inputs.input_ids.shape[1]:]
        response = tokenizer.decode(new_tokens, skip_special_tokens=True)
        
        return response.strip()
        
    except Exception as e:
        return f"Error: {e}"

def main():
    print("=" * 40)
    print("🤖 Basic Phi-3 Chat Test")
    print("=" * 40)
    
    # Try to load model
    print("Loading model...")
    model, tokenizer = load_model()
    
    if model is None:
        print("❌ Could not load model. Check your setup.")
        input("Press Enter to exit...")
        return
    
    print("\n✅ Model loaded! Type 'quit' to exit.\n")
    
    while True:
        try:
            user_input = input("You: ").strip()
            
            if user_input.lower() in ['quit', 'exit']:
                break
                
            if not user_input:
                continue
            
            print("Thinking...")
            response = simple_generate(model, tokenizer, user_input)
            print(f"Bot: {response}\n")
            
        except KeyboardInterrupt:
            print("\nBye!")
            break
        except Exception as e:
            print(f"Error: {e}")

if __name__ == "__main__":
    main()
