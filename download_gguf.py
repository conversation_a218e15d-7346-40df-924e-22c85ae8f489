#!/usr/bin/env python3
"""
Script to help download GGUF version of Phi-3 which is easier to load
"""

import os
import subprocess
import sys

def check_huggingface_hub():
    """Check if huggingface_hub is installed."""
    try:
        import huggingface_hub
        return True
    except ImportError:
        return False

def install_huggingface_hub():
    """Install huggingface_hub."""
    print("📦 Installing huggingface_hub...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "huggingface_hub"])
        return True
    except subprocess.CalledProcessError:
        return False

def download_gguf():
    """Download GGUF version of Phi-3."""
    try:
        from huggingface_hub import hf_hub_download
        
        print("🔄 Downloading GGUF version of Phi-3...")
        print("   This is much easier to load than the sharded version!")
        print("   File size: ~2.4GB")
        print()
        
        # Download the Q4_K_M quantized version (good balance of quality and size)
        filename = "Phi-3-mini-4k-instruct-q4.gguf"
        repo_id = "microsoft/Phi-3-mini-4k-instruct-gguf"
        
        print(f"📥 Downloading {filename}...")
        print("   This may take 10-20 minutes depending on your internet speed...")
        
        local_path = hf_hub_download(
            repo_id=repo_id,
            filename=filename,
            local_dir="./phi-3-gguf",
            local_dir_use_symlinks=False
        )
        
        print(f"✅ Downloaded to: {local_path}")
        print()
        print("💡 To use this GGUF file, you'll need to install llama-cpp-python:")
        print("   pip install llama-cpp-python")
        print()
        print("📝 Then you can use it with a simple script like:")
        print("   from llama_cpp import Llama")
        print("   llm = Llama(model_path='./phi-3-gguf/Phi-3-mini-4k-instruct-q4.gguf')")
        
        return True
        
    except Exception as e:
        print(f"❌ Download failed: {e}")
        return False

def create_gguf_chat_script():
    """Create a simple chat script for GGUF."""
    script_content = '''#!/usr/bin/env python3
"""
Simple chat script for GGUF version of Phi-3
"""

try:
    from llama_cpp import Llama
except ImportError:
    print("❌ llama-cpp-python not installed")
    print("Install with: pip install llama-cpp-python")
    exit(1)

def main():
    print("🔄 Loading GGUF model...")
    
    try:
        llm = Llama(
            model_path="./phi-3-gguf/Phi-3-mini-4k-instruct-q4.gguf",
            n_ctx=2048,  # Context length
            n_threads=4,  # Number of CPU threads
            verbose=False
        )
        
        print("✅ Model loaded!")
        print("💬 Chat ready (type 'quit' to exit)\\n")
        
        while True:
            user_input = input("You: ").strip()
            
            if user_input.lower() in ['quit', 'exit']:
                break
            
            if not user_input:
                continue
            
            print("🤔 Thinking...")
            
            # Generate response
            response = llm(
                f"<|system|>You are a helpful assistant.<|end|><|user|>{user_input}<|end|><|assistant|>",
                max_tokens=200,
                temperature=0.7,
                stop=["<|end|>", "<|user|>"]
            )
            
            print(f"Bot: {response['choices'][0]['text'].strip()}\\n")
    
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
'''
    
    with open("gguf_chat.py", "w") as f:
        f.write(script_content)
    
    print("📝 Created gguf_chat.py script")

def main():
    """Main function."""
    print("=" * 60)
    print("📦 GGUF Version Downloader for Phi-3")
    print("=" * 60)
    print()
    print("The GGUF version is much easier to load and doesn't have")
    print("the checkpoint sharding issues you're experiencing.")
    print()
    
    # Check if huggingface_hub is available
    if not check_huggingface_hub():
        print("📦 huggingface_hub not found. Installing...")
        if not install_huggingface_hub():
            print("❌ Failed to install huggingface_hub")
            print("Please install manually: pip install huggingface_hub")
            input("Press Enter to exit...")
            return
    
    choice = input("Would you like to download the GGUF version? (y/n): ").lower()
    
    if choice == 'y':
        if download_gguf():
            create_gguf_chat_script()
            print()
            print("🎉 Setup complete!")
            print("Next steps:")
            print("1. Install llama-cpp-python: pip install llama-cpp-python")
            print("2. Run the chat: python gguf_chat.py")
        else:
            print("❌ Download failed. Please check your internet connection.")
    else:
        print("👍 No problem. You can download manually from:")
        print("https://huggingface.co/microsoft/Phi-3-mini-4k-instruct-gguf")
    
    input("Press Enter to exit...")

if __name__ == "__main__":
    main()
