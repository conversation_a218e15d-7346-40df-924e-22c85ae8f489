#!/usr/bin/env python3
"""
Simple chat script for Phi-3 Mini - Basic version to avoid common loading issues
"""

import os
import torch
from transformers import AutoModelForCausalLM, AutoTokenizer
import warnings

# Suppress warnings
warnings.filterwarnings("ignore")

def load_model_simple():
    """Load model with basic settings to avoid common issues."""
    model_path = r".\phi-3-mini"
    
    print("🔄 Loading Phi-3 Mini (simple mode)...")
    print(f"📁 Model path: {model_path}")
    
    try:
        # Load tokenizer first
        print("📝 Loading tokenizer...")
        tokenizer = AutoTokenizer.from_pretrained(model_path, trust_remote_code=True)
        
        # Add padding token if missing
        if tokenizer.pad_token is None:
            tokenizer.pad_token = tokenizer.eos_token
        
        print("🧠 Loading model...")
        # Load model with minimal settings to avoid issues
        model = AutoModelForCausalLM.from_pretrained(
            model_path,
            torch_dtype=torch.float32,  # Use float32 for compatibility
            device_map="cpu",  # Force CPU to avoid GPU issues
            trust_remote_code=True,
            low_cpu_mem_usage=True,  # Help with memory
            use_safetensors=True  # Use safetensors format
        )
        
        print("✅ Model loaded successfully!")
        return model, tokenizer
        
    except Exception as e:
        print(f"❌ Error loading model: {e}")
        return None, None

def generate_response(model, tokenizer, user_input, max_length=200):
    """Generate a simple response."""
    try:
        # Format input in Phi-3 chat format
        prompt = f"<|system|>\nYou are a helpful assistant.<|end|>\n<|user|>\n{user_input}<|end|>\n<|assistant|>\n"
        
        # Tokenize
        inputs = tokenizer(prompt, return_tensors="pt", padding=True, truncation=True)
        
        print("🤔 Generating response...")
        
        # Generate
        with torch.no_grad():
            outputs = model.generate(
                inputs.input_ids,
                attention_mask=inputs.attention_mask,
                max_length=inputs.input_ids.shape[1] + max_length,
                num_return_sequences=1,
                temperature=0.7,
                do_sample=True,
                pad_token_id=tokenizer.eos_token_id,
                eos_token_id=tokenizer.eos_token_id,
                no_repeat_ngram_size=3
            )
        
        # Decode response
        response = tokenizer.decode(outputs[0], skip_special_tokens=True)
        
        # Extract just the assistant's response
        if "<|assistant|>" in response:
            response = response.split("<|assistant|>")[-1].strip()
        
        return response
        
    except Exception as e:
        return f"❌ Error generating response: {e}"

def main():
    """Simple chat loop."""
    print("=" * 50)
    print("🤖 Simple Phi-3 Chat")
    print("=" * 50)
    print("Loading model (this may take a moment)...")
    
    # Load model
    model, tokenizer = load_model_simple()
    
    if model is None or tokenizer is None:
        print("❌ Failed to load model. Exiting.")
        input("Press Enter to exit...")
        return
    
    print("\n✅ Ready to chat!")
    print("Type 'quit' to exit\n")
    
    while True:
        try:
            user_input = input("You: ").strip()
            
            if user_input.lower() in ['quit', 'exit', 'bye']:
                print("👋 Goodbye!")
                break
            
            if not user_input:
                continue
            
            response = generate_response(model, tokenizer, user_input)
            print(f"Bot: {response}\n")
            
        except KeyboardInterrupt:
            print("\n👋 Chat interrupted. Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
