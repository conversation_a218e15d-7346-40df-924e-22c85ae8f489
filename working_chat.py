#!/usr/bin/env python3
"""
Working chat script that specifically avoids flash attention and handles sharded loading
"""

import os
import torch
from transformers import AutoModelForCausalLM, AutoTokenizer
import warnings

# Suppress warnings
warnings.filterwarnings("ignore")

def load_model_no_flash_attention():
    """Load model with flash attention explicitly disabled."""
    model_path = r".\phi-3-mini"
    
    print("🔄 Loading Phi-3 Mini (no flash attention)...")
    print(f"📁 Model path: {model_path}")
    
    try:
        # Load tokenizer first
        print("📝 Loading tokenizer...")
        tokenizer = AutoTokenizer.from_pretrained(
            model_path, 
            trust_remote_code=True
        )
        
        # Set padding token
        if tokenizer.pad_token is None:
            tokenizer.pad_token = tokenizer.eos_token
        
        print("🧠 Loading model (this may take a few minutes)...")
        print("   ⏳ Loading checkpoint shards...")
        
        # Load model with flash attention explicitly disabled
        model = AutoModelForCausalLM.from_pretrained(
            model_path,
            trust_remote_code=True,
            torch_dtype=torch.float16,  # Use float16 to save memory
            device_map="cpu",  # Force CPU
            attn_implementation="eager",  # Explicitly disable flash attention
            use_safetensors=True,
            low_cpu_mem_usage=True,
            # Add these to help with sharded loading
            offload_folder=None,
            offload_state_dict=False
        )
        
        print("✅ Model loaded successfully!")
        return model, tokenizer
        
    except Exception as e:
        print(f"❌ Loading failed: {e}")
        
        # Try alternative loading method
        print("\n🔄 Trying alternative loading method...")
        try:
            # Even more explicit about disabling flash attention
            from transformers.models.phi3.modeling_phi3 import Phi3Attention
            
            # Monkey patch to ensure no flash attention
            original_forward = Phi3Attention.forward
            
            model = AutoModelForCausalLM.from_pretrained(
                model_path,
                trust_remote_code=True,
                torch_dtype=torch.float32,  # Use float32 for maximum compatibility
                device_map={"": "cpu"},  # More explicit CPU mapping
                use_safetensors=True,
                low_cpu_mem_usage=True,
                _attn_implementation="eager"  # Alternative way to set attention
            )
            
            print("✅ Alternative loading successful!")
            return model, tokenizer
            
        except Exception as e2:
            print(f"❌ Alternative loading also failed: {e2}")
            return None, None

def generate_response(model, tokenizer, user_input, max_tokens=100):
    """Generate response with proper Phi-3 formatting."""
    try:
        # Use Phi-3 chat format
        messages = [
            {"role": "system", "content": "You are a helpful assistant."},
            {"role": "user", "content": user_input}
        ]
        
        # Apply chat template
        prompt = tokenizer.apply_chat_template(
            messages, 
            tokenize=False, 
            add_generation_prompt=True
        )
        
        # Tokenize
        inputs = tokenizer(prompt, return_tensors="pt", padding=True)
        
        print("🤔 Generating response...")
        
        # Generate with explicit settings
        with torch.no_grad():
            outputs = model.generate(
                inputs.input_ids,
                attention_mask=inputs.attention_mask,
                max_new_tokens=max_tokens,
                temperature=0.7,
                do_sample=True,
                pad_token_id=tokenizer.eos_token_id,
                eos_token_id=tokenizer.eos_token_id,
                repetition_penalty=1.1,
                no_repeat_ngram_size=3
            )
        
        # Decode only the new tokens
        new_tokens = outputs[0][inputs.input_ids.shape[1]:]
        response = tokenizer.decode(new_tokens, skip_special_tokens=True)
        
        return response.strip()
        
    except Exception as e:
        return f"❌ Generation error: {e}"

def main():
    """Main chat function."""
    print("=" * 50)
    print("🤖 Phi-3 Chat (Flash Attention Disabled)")
    print("=" * 50)
    
    # Load model
    model, tokenizer = load_model_no_flash_attention()
    
    if model is None or tokenizer is None:
        print("❌ Failed to load model.")
        print("\n💡 Troubleshooting tips:")
        print("   1. Make sure you have enough RAM (8GB+ recommended)")
        print("   2. Close other applications to free memory")
        print("   3. Try restarting your computer")
        input("Press Enter to exit...")
        return
    
    print("\n✅ Model ready! Starting chat...")
    print("Commands: 'quit' to exit, 'clear' to clear screen")
    print("=" * 50)
    
    conversation_count = 0
    
    while True:
        try:
            user_input = input("\n💬 You: ").strip()
            
            if user_input.lower() in ['quit', 'exit', 'bye']:
                print("👋 Goodbye!")
                break
            
            if user_input.lower() == 'clear':
                os.system('cls' if os.name == 'nt' else 'clear')
                print("🧹 Screen cleared!")
                continue
            
            if not user_input:
                continue
            
            # Generate response
            response = generate_response(model, tokenizer, user_input)
            print(f"\n🤖 Phi-3: {response}")
            
            conversation_count += 1
            
            # Memory management - suggest clearing after many exchanges
            if conversation_count % 10 == 0:
                print(f"\n💡 Tip: You've had {conversation_count} exchanges. Type 'clear' if responses get slow.")
            
        except KeyboardInterrupt:
            print("\n\n👋 Chat interrupted. Goodbye!")
            break
        except Exception as e:
            print(f"\n❌ Error: {e}")
            print("💡 Try typing your message again or type 'quit' to exit.")

if __name__ == "__main__":
    main()
