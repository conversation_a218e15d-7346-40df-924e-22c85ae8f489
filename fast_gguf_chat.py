#!/usr/bin/env python3
"""
Super-fast GGUF chat - optimized for speed over everything else
"""

import os
import sys
import time
import multiprocessing

def load_fast_model(model_path):
    """Load model with maximum speed optimizations."""
    print(f"🚀 Loading model for MAXIMUM SPEED: {os.path.basename(model_path)}")
    
    try:
        from llama_cpp import Llama
        
        cpu_cores = multiprocessing.cpu_count()
        threads = min(cpu_cores, 6)  # Optimal for most systems
        
        print(f"   🔧 Using {threads} threads")
        print(f"   🔧 Reduced context length for speed")
        print(f"   🔧 Aggressive optimizations enabled")
        
        llm = Llama(
            model_path=model_path,
            n_ctx=1024,  # Very small context for maximum speed
            n_threads=threads,
            n_gpu_layers=0,
            verbose=False,
            use_mmap=True,
            use_mlock=False,
            n_batch=128,  # Small batch size
            f16_kv=True,
            logits_all=False,
            vocab_only=False,
            embedding=False,
            last_n_tokens_size=32,  # Very small for speed
            seed=-1,  # Random seed
            n_parts=1,
        )
        
        print("✅ SPEED-OPTIMIZED model loaded!")
        return llm
        
    except Exception as e:
        print(f"❌ Failed to load: {e}")
        return None

def fast_generate(llm, user_input):
    """Super fast generation with minimal settings."""
    try:
        # Minimal prompt
        prompt = f"Q: {user_input}\nA:"
        
        # Speed-first generation
        response = llm(
            prompt,
            max_tokens=50,  # Very short responses for speed
            temperature=0.3,  # Lower temperature for faster, more focused responses
            top_p=0.9,
            top_k=20,  # Small top-k for speed
            repeat_penalty=1.0,  # No penalty for max speed
            stop=["\nQ:", "\n\n", "Q:", "A:"],
            echo=False,
            stream=False,
        )
        
        text = response['choices'][0]['text'].strip()
        
        # Clean up
        if text.startswith("A:"):
            text = text[2:].strip()
        
        return text
        
    except Exception as e:
        return f"Error: {e}"

def find_gguf():
    """Quick GGUF finder."""
    paths = [
        r".\phi-3-gguf\Phi-3-mini-4k-instruct-q4.gguf",
        r".\Phi-3-mini-4k-instruct-q4.gguf",
    ]
    
    for path in paths:
        if os.path.exists(path):
            return path
    
    # Quick scan
    for item in os.listdir("."):
        if item.endswith(".gguf"):
            return item
        if os.path.isdir(item) and "gguf" in item.lower():
            for subitem in os.listdir(item):
                if subitem.endswith(".gguf"):
                    return os.path.join(item, subitem)
    
    return None

def main():
    """Super fast chat."""
    print("⚡ SUPER FAST Phi-3 Chat")
    print("=" * 30)
    print("Optimized for SPEED - shorter responses, faster generation")
    print()
    
    # Check llama-cpp-python
    try:
        from llama_cpp import Llama
    except ImportError:
        print("❌ Need: pip install llama-cpp-python")
        input("Press Enter to exit...")
        return
    
    # Find model
    model_path = find_gguf()
    if not model_path:
        print("❌ No GGUF model found")
        input("Press Enter to exit...")
        return
    
    # Load model
    llm = load_fast_model(model_path)
    if not llm:
        input("Press Enter to exit...")
        return
    
    print("\n⚡ FAST CHAT READY!")
    print("Type 'quit' to exit, 'help' for tips")
    print("=" * 30)
    
    while True:
        try:
            user_input = input("\n⚡ You: ").strip()
            
            if user_input.lower() in ['quit', 'exit']:
                break
            
            if user_input.lower() == 'help':
                print("\n💡 SPEED TIPS:")
                print("   • Keep questions short")
                print("   • Responses limited to ~50 tokens for speed")
                print("   • Ask simple, direct questions")
                print("   • Type 'quit' to exit")
                continue
            
            if not user_input:
                continue
            
            # Time the generation
            start = time.time()
            response = fast_generate(llm, user_input)
            end = time.time()
            
            print(f"🤖 Bot: {response}")
            print(f"⏱️  {end-start:.1f}s")
            
        except KeyboardInterrupt:
            print("\n👋 Bye!")
            break
        except Exception as e:
            print(f"Error: {e}")

if __name__ == "__main__":
    main()
