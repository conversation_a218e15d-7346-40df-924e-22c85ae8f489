#!/usr/bin/env python3
"""
Minimal chat script - most basic loading possible
"""

import torch
from transformers import AutoModelForCausalLM, AutoTokenizer
import warnings
warnings.filterwarnings("ignore")

def load_minimal():
    """Most minimal loading possible."""
    print("🔄 Minimal loading (please wait 2-3 minutes)...")
    
    model_path = r".\phi-3-mini"
    
    try:
        # Load tokenizer
        print("📝 Loading tokenizer...")
        tokenizer = AutoTokenizer.from_pretrained(model_path)
        
        # Load model with absolute minimal settings
        print("🧠 Loading model (this will take time)...")
        model = AutoModelForCausalLM.from_pretrained(
            model_path,
            torch_dtype=torch.float32,
            device_map="cpu"
        )
        
        print("✅ Success!")
        return model, tokenizer
        
    except Exception as e:
        print(f"❌ Failed: {e}")
        return None, None

def chat_minimal(model, tokenizer):
    """Minimal chat."""
    print("\n💬 Chat ready! Type 'quit' to exit.\n")
    
    while True:
        user_input = input("You: ").strip()
        
        if user_input.lower() in ['quit', 'exit']:
            break
        
        if not user_input:
            continue
        
        try:
            # Simple prompt
            prompt = f"Human: {user_input}\nAssistant:"
            inputs = tokenizer(prompt, return_tensors="pt")
            
            # Generate
            with torch.no_grad():
                outputs = model.generate(
                    inputs.input_ids,
                    max_new_tokens=50,
                    temperature=0.7,
                    do_sample=True,
                    pad_token_id=tokenizer.eos_token_id
                )
            
            # Decode response
            response = tokenizer.decode(
                outputs[0][inputs.input_ids.shape[1]:], 
                skip_special_tokens=True
            )
            
            print(f"Bot: {response.strip()}\n")
            
        except Exception as e:
            print(f"Error: {e}\n")

def main():
    print("🤖 Minimal Phi-3 Chat")
    print("=" * 30)
    
    model, tokenizer = load_minimal()
    
    if model and tokenizer:
        chat_minimal(model, tokenizer)
    else:
        print("❌ Could not load model")
        input("Press Enter to exit...")

if __name__ == "__main__":
    main()
