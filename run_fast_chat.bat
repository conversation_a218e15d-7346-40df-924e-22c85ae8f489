@echo off
echo ⚡ Starting SUPER FAST Phi-3 Chat...
echo.

REM Activate virtual environment
call phi3_env\Scripts\activate.bat

if errorlevel 1 (
    echo ❌ Failed to activate virtual environment
    echo Trying without virtual environment...
    echo.
    goto :run_script
)

echo ✅ Virtual environment activated

:run_script
echo ⚡ Starting SPEED-OPTIMIZED chat...
echo.

REM Run the fast chat script
python fast_gguf_chat.py

echo.
echo 👋 Fast chat session ended
pause
